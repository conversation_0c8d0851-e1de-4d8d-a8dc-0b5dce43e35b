<template>
  <div class="grid-menu-skeleton">
    <div class="skeleton-grid-container">
      <div v-for="i in 5" :key="i" class="skeleton-grid-item">
        <div class="skeleton-icon"></div>
        <div class="skeleton-title"></div>
        <div class="skeleton-subtitle"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 骨架屏组件不需要任何逻辑
</script>

<style scoped lang="less">
// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.grid-menu-skeleton {
  // 与 BFHomeView.vue 中 .grid-menu-container 样式完全一致
  background: #ffffff;
  border-radius: 12px;
  margin: 8px 12px;
  // 与 IconGrid 组件的 padding: 5px 保持一致
  padding: 5px;

  .skeleton-grid-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px; // 与 IconGrid 的 gap: 8px 保持一致

    .skeleton-grid-item {
      width: calc(20% - 6.4px); // 5列布局，减去gap的影响
      display: flex;
      flex-direction: column;
      align-items: center;
      // 与 IconGrid 的 padding: 6px 保持一致
      padding: 6px;
      text-align: center;
      box-sizing: border-box;

      .skeleton-icon {
        .skeleton-base();
        // 与 IconGrid 的图标尺寸 34px 保持一致
        width: 34px;
        height: 34px;
        border-radius: 4px;
        // 与 IconGrid 的 margin-bottom: 8px 保持一致
        margin-bottom: 8px;
      }

      .skeleton-title {
        .skeleton-base();
        width: 80%;
        // 与 IconGrid 的 font-size: 12px 对应的高度
        height: 12px;
        margin-bottom: 2px;
      }

      .skeleton-subtitle {
        .skeleton-base();
        width: 60%;
        // 与 IconGrid 的 font-size: 10px 对应的高度
        height: 10px;
      }
    }
  }
}

// 移动端适配 - 与 IconGrid 保持一致
@media (max-width: 375px) {
  .grid-menu-skeleton {
    .skeleton-grid-container {
      .skeleton-grid-item {
        // 保持与桌面端相同的 padding: 6px
        padding: 6px;

        .skeleton-icon {
          // 移动端图标尺寸保持 34px，与 IconGrid 一致
          width: 34px;
          height: 34px;
          margin-bottom: 8px;
        }

        .skeleton-title {
          height: 12px;
          margin-bottom: 2px;
        }

        .skeleton-subtitle {
          height: 10px;
        }
      }
    }
  }
}
</style>
