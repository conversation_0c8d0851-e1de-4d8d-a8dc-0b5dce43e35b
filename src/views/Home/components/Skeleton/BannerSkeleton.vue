<template>
  <div class="banner-skeleton">
    <div class="skeleton-banner">
      <div class="skeleton-image"></div>
    </div>
  </div>
</template>

<script setup>
// 骨架屏组件不需要任何逻辑
</script>

<style scoped lang="less">
// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.banner-skeleton {
  border-radius: 12px;
  overflow: hidden;

  .skeleton-banner {
    position: relative;
    width: 100%;
    height: 120px; // 移动端默认高度，相对较低
    background: #ffffff; // 与实际组件背景色一致
    border-radius: 12px;

    .skeleton-image {
      .skeleton-base();
      width: 100%;
      height: 100%;
      border-radius: 12px;
      background-color: #f8f9fa; // 图片加载前的背景色
    }

    // 模拟分页器（分数指示器）
    .skeleton-pagination {
      position: absolute;
      bottom: 12px;
      right: 12px;
      background: rgba(0, 0, 0, 0.6);
      padding: 6px 12px;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.1);

      .skeleton-fraction {
        .skeleton-base();
        width: 24px;
        height: 13px;
        border-radius: 2px;
        background: rgba(255, 255, 255, 0.8);
        animation: none;
      }
    }
  }
}
</style>
