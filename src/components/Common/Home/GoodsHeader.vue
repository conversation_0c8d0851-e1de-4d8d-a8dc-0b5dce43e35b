<template>
  <div class="goods-header">
    <div class="goods-header__container">
      <div
        ref="typeRefs"
        v-for="(item, index) in typeList"
        :key="index"
        class="goods-header__item"
        :class="{ 'goods-header__item--active': activeIndex === index }"
        @click="chooseOne(index)"
      >
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'

const props = defineProps({
  typeList: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['switchTabs'])

const typeRefs = ref([])
const activeIndex = ref(0)

const chooseOne = (index) => {
  activeIndex.value = index
  emit('switchTabs', props.typeList[index].id)
}

onMounted(() => {
  nextTick(() => {
    if (props.typeList.length > 0) {
      activeIndex.value = 0
    }
  })
})
</script>

<style lang="less" scoped>
.goods-header {
  width: 100vw;
  position: relative;
  padding-bottom: 10px;
  overflow-x: hidden;
  z-index: 98;

  &__container {
    font-size: @font-size-14;
    height: 25px;
    margin-top: 15px;
    display: flex;
    overflow-x: scroll;
    .no-scrollbar();
  }

  &__item {
    white-space: nowrap;
    height: 14px;
    line-height: 14px;
    padding: 0 17px;
    transition: 0.3s;
    color: @text-color-secondary;
    cursor: pointer;

    &:not(:last-child) {
      border-right: 1px solid @divider-color-base;
    }

    &--active {
      position: relative;
      z-index: 2;
      color: @text-color-primary;

      &::after {
        content: "";
        display: block;
        position: absolute;
        left: 50%;
        bottom: -1px;
        width: 56px;
        height: 6px;
        border-radius: @radius-10;
        z-index: -1;
        transform: translateX(-50%);
        background: @gradient-orange-106;
        transition: 0.3s;
      }
    }
  }
}
</style>
