<template>
  <div class="grid-menu" :class="`columns-${dynamicColumns}`">
    <div class="grid-container">
      <div v-for="(item, index) in menuItems" :key="index" class="grid-item" :class="{ 'grid-item-more': item.isMore }"
        @click="handleItemClick(item, index)">
        <div class="item-content">
          <!-- 图标 -->
          <div class="item-icon">
            <img v-if="item.icon" :src="item.icon" :alt="item.title" />
            <div v-else class="icon-placeholder">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" />
              </svg>
            </div>
          </div>

          <!-- 标题 -->
          <div class="item-title">{{ item.title }}</div>

          <!-- 副标题/描述 -->
          <div v-if="item.subtitle" class="item-subtitle">{{ item.subtitle }}</div>

          <!-- 角标 -->
          <div v-if="item.badge" class="item-badge">{{ item.badge }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'

const props = defineProps({
  // 菜单项数据
  items: {
    type: Array,
    default: () => []
  },
  // 列数 (2, 4, 5)
  columns: {
    type: Number,
    default: 5,
    validator: (value) => value === 2 || value === 4 || value === 5
  },
  // 是否显示更多按钮
  showMore: {
    type: Boolean,
    default: true
  },
  // 最大显示数量
  maxItems: {
    type: Number,
    default: 10
  }
})

const { items, columns, showMore, maxItems } = toRefs(props)

const emit = defineEmits(['itemClick', 'moreClick'])

// 计算菜单项
const menuItems = computed(() => {
  let itemList = [...items.value]

  // 如果超过最大数量且需要显示更多按钮
  if (showMore.value && itemList.length > maxItems.value - 1) {
    itemList = itemList.slice(0, maxItems.value - 1)
    itemList.push({
      title: '更多',
      icon: null,
      isMore: true
    })
  } else if (itemList.length > maxItems.value) {
    itemList = itemList.slice(0, maxItems.value)
  }

  return itemList
})

// 动态计算列数
const dynamicColumns = computed(() => {
  const itemCount = menuItems.value.length

  // 如果数据够，就以 columns 为准
  if (itemCount >= columns.value) {
    return columns.value
  }

  // 如果数据不够，动态计算列数
  // 确保不超过最大列数 (columns)
  return Math.min(itemCount, columns.value)
})

// 计算每个项目的宽度百分比
const itemWidth = computed(() => {
  return `${100 / dynamicColumns.value}%`
})

// 处理项目点击
const handleItemClick = (item, index) => {
  if (item.isMore) {
    emit('moreClick')
  } else {
    emit('itemClick', { item, index })
  }
}
</script>

<style scoped lang="less">
.grid-menu {
  padding: @padding-page;
  //background: @bg-color-white;

  .grid-container {
    display: flex;
    flex-wrap: wrap;
    gap: @radius-8;
  }

  .grid-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    //padding: @radius-6;
    width: calc(v-bind(itemWidth) - @radius-8);
    box-sizing: border-box;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
      transform: translateY(-1px);

      .item-icon {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
      }
    }

    &:active {
      transform: translateY(0);
      background-color: rgba(0, 0, 0, 0.04);

      .item-icon {
        transform: scale(0.95);
      }
    }

    .item-content {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
    }

    .item-icon {
      width: 45px;
      height: 45px;
      margin-bottom: 3px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      img {
        width: 34px;
        height: 34px;
        object-fit: contain;
      }

      .icon-placeholder {
        color: @text-color-tertiary;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .item-title {
      font-size: @font-size-12;
      font-weight: @font-weight-500;
      color: @text-color-primary;
      text-align: center;
      line-height: 1.2;
      max-width: 100%;
      .ellipsis();
    }

    .item-subtitle {
      font-size: @font-size-11;
      color: @text-color-tertiary;
      text-align: center;
      margin-top: @radius-2;
      line-height: 1.2;
      max-width: 100%;
      .ellipsis();
    }

    .item-badge {
      position: absolute;
      top: -@radius-2;
      right: -@radius-2;
      background: linear-gradient(135deg, @color-red 0%, darken(@color-red, 10%) 100%);
      color: @color-white;
      font-size: @font-size-11;
      font-weight: @font-weight-600;
      padding: @radius-2 @radius-6;
      border-radius: @radius-8;
      min-width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 1px 3px rgba(255, 107, 107, 0.3);
      transform: scale(0.9);
    }

    &.grid-item-more {
      .item-icon {
        background: linear-gradient(135deg, @bg-color-gray 0%, darken(@bg-color-gray, 5%) 100%);
        border: 2px dashed @divider-color-base;
        box-shadow: none;

        &::after {
          content: '';
          width: 20px;
          height: 20px;
          background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2'%3E%3Cpath d='M12 5v14M5 12h14'/%3E%3C/svg%3E") no-repeat center;
          background-size: contain;
        }
      }

      .item-title {
        color: @text-color-secondary;
      }

      &:hover .item-icon {
        background: linear-gradient(135deg, darken(@bg-color-gray, 5%) 0%, darken(@bg-color-gray, 10%) 100%);
        border-color: darken(@divider-color-base, 10%);
      }
    }
  }
}

.grid-menu {
  &.columns-2 {
    .grid-item {
      flex-direction: row;
      text-align: left;
      //padding: @radius-10;

      .item-content {
        flex-direction: row;
        align-items: center;
      }

      .item-icon {
        width: 48px;
        height: 48px;
        margin-bottom: 0;
        margin-right: @radius-12;
        flex-shrink: 0;

        img {
          width: 36px;
          height: 36px;
        }
      }

      .item-title {
        font-size: @font-size-14;
        text-align: left;
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
      }

      .item-subtitle {
        text-align: left;
        margin-top: @radius-4;
      }
    }
  }
}
</style>
