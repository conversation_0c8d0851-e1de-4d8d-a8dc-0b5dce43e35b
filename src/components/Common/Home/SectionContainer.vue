<template>
    <section class="home-block">
        <div class="header">
            <h2 class="title">{{ title }}</h2>
            <p class="more" v-if="more" @click="onClick">
                <span>{{ more }}</span>
                <span class="icon-arrow" />
            </p>
        </div>
        <div class="content">
            <slot />
        </div>
    </section>
</template>

<script setup>
import { toRefs } from 'vue'

const props = defineProps({
    title: {
        type: String,
        required: true
    },
    more: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['click'])

const { title, more } = toRefs(props)

const onClick = () => {
    emit('click')
}
</script>

<style lang="less" scoped>
.home-block {
    margin: @radius-10 0;
    //background: @bg-color-white;
    overflow: hidden;
    transition: box-shadow 0.3s ease;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: @radius-10 @radius-20 @radius-10;

        .title {
            font-size: @font-size-18;
            font-weight: @font-weight-600;
            color: @text-color-primary;
            line-height: 1.4;
            margin: 0;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                left: -@radius-12;
                top: 50%;
                transform: translateY(-50%);
                width: @radius-4;
                height: 16px;
                background: linear-gradient(135deg, @color-red, @color-orange);
                border-radius: @radius-2;
            }
        }

        .more {
            display: flex;
            align-items: center;
            font-size: @font-size-14;
            font-weight: @font-weight-400;
            color: @text-color-secondary;
            cursor: pointer;
            padding: @radius-6 @radius-12;
            border-radius: @radius-20;
            background: @bg-color-gray;
            border: 1px solid transparent;
            transition: all 0.3s ease;

            &:hover {
                color: @color-red;
                background: @bg-color-white;
                border-color: @color-red;
                transform: translateY(-1px);
            }

            .icon-arrow {
                margin-left: @radius-6;
                width: @radius-12;
                height: @radius-12;
                background-image: url('./assets/arrow.png');
                background-repeat: no-repeat;
                background-size: contain;
                background-position: center;
                transition: transform 0.3s ease;
                opacity: @opacity-07;
            }

            &:hover .icon-arrow {
                transform: translateX(3px);
                opacity: 1;
            }
        }
    }

    .content {
        padding: @radius-10;
        //background: @bg-color-white;
    }
}
</style>
