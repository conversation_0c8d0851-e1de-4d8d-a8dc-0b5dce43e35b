<template>
  <div class="recommend-skeleton">
    <div 
      v-for="i in 4" 
      :key="i" 
      class="recommend-section-skeleton"
    >
      <div class="title-skeleton">
        <div class="skeleton-title-left"></div>
        <div class="skeleton-title-right"></div>
      </div>
      <div class="commodity-skeleton">
        <div class="skeleton-commodity-left"></div>
        <div class="skeleton-commodity-right"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 推荐区域骨架屏组件
</script>

<style lang="less" scoped>
.recommend-skeleton {
  margin-top: 10px;
  padding: 0 @padding-page;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}

.recommend-section-skeleton {
  width: calc(50% - 5px);
  background: @bg-color-white;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-radius: @radius-10;
  margin-bottom: 10px;
}

.title-skeleton {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 10px;
}

.skeleton-title-left {
  width: 80px;
  height: 20px;
  background: #f0f0f0;
  border-radius: @radius-4;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-title-right {
  width: 65px;
  height: 20px;
  background: #f0f0f0;
  border-radius: @radius-4;
  animation: skeleton-loading 1.5s ease-in-out infinite;
  animation-delay: 0.1s;
}

.commodity-skeleton {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 10px;
  margin-bottom: 27px;
}

.skeleton-commodity-left,
.skeleton-commodity-right {
  width: calc(50% - 5px);
  height: 80px;
  background: #f0f0f0;
  border-radius: @radius-6;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-commodity-right {
  animation-delay: 0.2s;
}

@keyframes skeleton-loading {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}
</style>